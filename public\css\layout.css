/* تنسيق الهيدر والقائمة */
header {
    background-color: var(--dark-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar {
    padding: 15px 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    position: relative;
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    color: var(--secondary-color);
    text-decoration: none;
}

.slogan {
    color: var(--light-color);
    font-style: italic;
    margin-right: 10px;
}

/* زر القائمة للأجهزة المحمولة */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--light-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.nav-links {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.nav-links li {
    margin-right: 20px;
}

.nav-links li:last-child {
    margin-right: 0;
}

.nav-links a {
    color: var(--light-color);
    font-weight: 500;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.nav-links a:hover, .nav-links a.active {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.nav-links a i {
    margin-left: 5px;
}

/* تجاوب القائمة للأجهزة المحمولة */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--dark-color);
        flex-direction: column;
        padding: 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        border-radius: 0 0 8px 8px;
        z-index: 1000;
    }

    .nav-links.active {
        display: flex !important;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .nav-links li {
        margin: 0;
        margin-bottom: 10px;
        width: 100%;
    }

    .nav-links li:last-child {
        margin-bottom: 0;
    }

    .nav-links a {
        display: block;
        width: 100%;
        text-align: center;
        padding: 12px 15px;
        border-radius: 6px;
    }
}

/* تنسيق الفوتر */
footer {
    background-color: var(--bg-dark);
    color: var(--text-light);
    padding-top: 60px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-about h3, .footer-links h3, .footer-contact h3 {
    color: var(--secondary-color);
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-about h3::after, .footer-links h3::after, .footer-contact h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: var(--text-light);
}

.footer-links ul li a:hover {
    color: var(--secondary-color);
}

.footer-contact p {
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    color: var(--text-light);
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background-color: var(--primary-color);
    color: var(--text-light);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 0;
    text-align: center;
}