/* تنسيقات صفحة المفكرين والدعاة */

/* عنوان الصفحة */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 30px 0;
    border-bottom: 2px solid var(--border-color);
}

.page-title-section {
    flex: 1;
}

.page-title {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-title i {
    color: var(--secondary-color);
}

.page-description {
    font-size: 1.1rem;
    /* color: var(--text-color); */
    color: azure;
    /* opacity: 0.8; */
    max-width: 600px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

/* شريط البحث والفلترة */
.search-filter-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 1.1rem;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(29, 138, 78, 0.1);
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-controls select {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.filter-controls select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* قسم المقالات */
.articles-section {
    margin-bottom: 40px;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.article-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.article-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: bold;
}

.article-content {
    padding: 20px;
}

.article-title {
    font-size: 1.3rem;
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 10px;
    line-height: 1.4;
}

.article-title a {
    color: inherit;
    text-decoration: none;
}

.article-title a:hover {
    color: var(--primary-color);
}

.article-meta {
    display: flex;
    gap: 15px;
    margin-bottom: 12px;
    font-size: 0.9rem;
    color: #666;
    flex-wrap: wrap;
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.article-excerpt {
    color: var(--text-color);
    line-height: 1.6;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-tags {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.tag {
    background: var(--bg-light);
    color: var(--primary-color);
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
}

.article-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.stats-left {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: #666;
}

.stats-left span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.read-more {
    background: var(--primary-color);
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: bold;
    transition: all 0.3s ease;
}

.read-more:hover {
    background: var(--dark-color);
    color: white;
}

/* رسالة عدم وجود نتائج */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-results i {
    font-size: 4rem;
    color: var(--border-color);
    margin-bottom: 20px;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--text-color);
}

/* الترقيم */
.pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.pagination {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination button {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    background: white;
    color: var(--text-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.pagination button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination button:disabled:hover {
    background: white;
    color: var(--text-color);
    border-color: var(--border-color);
}

/* النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    overflow-y: auto;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: var(--bg-light);
    color: var(--primary-color);
}

.modal-body {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(29, 138, 78, 0.1);
}

/* محرر النصوص الغني */
.editor-toolbar {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
    padding: 10px;
    background: var(--bg-light);
    border-radius: 8px 8px 0 0;
    border: 2px solid var(--border-color);
    border-bottom: none;
}

.editor-btn {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.editor-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.rich-editor {
    min-height: 300px;
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: 0 0 8px 8px;
    background: white;
    line-height: 1.6;
    font-size: 1rem;
}

.rich-editor:focus {
    outline: none;
    border-color: var(--primary-color);
}

.rich-editor[placeholder]:empty:before {
    content: attr(placeholder);
    color: #999;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    margin-top: 20px;
}

/* التجاوب */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }
    
    .search-filter-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-box {
        min-width: auto;
    }
    
    .filter-controls {
        justify-content: stretch;
    }
    
    .filter-controls select {
        flex: 1;
        min-width: auto;
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .article-stats {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }
    
    .editor-toolbar {
        flex-wrap: wrap;
    }
}
