/**
 * أنماط صفحة الملف الشخصي
 */

/* تخطيط عام */
.main-content {
    padding: 20px 0;
    min-height: calc(100vh - 160px);
    background: #f8f9fa;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-header h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.page-header p {
    color: #666;
    font-size: 1.1rem;
}

/* محتوى الملف الشخصي */
.profile-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    align-items: start;
}

/* بطاقة الملف الشخصي */
.profile-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    position: sticky;
    top: 20px;
}

.profile-header {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: white;
    padding: 30px;
    text-align: center;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 2rem;
}

.profile-info h2 {
    margin: 0 0 10px 0;
    font-size: 1.5rem;
}

.profile-info p {
    margin: 0 0 15px 0;
    opacity: 0.9;
}

.role-badge {
    background: rgba(255,255,255,0.2);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.profile-stats {
    padding: 20px;
    background: #f8f9fa;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 20px;
}

.stat-item div span {
    display: block;
    color: #666;
    font-size: 0.9rem;
}

.stat-item div strong {
    color: #333;
    font-size: 1rem;
}

/* نماذج التحرير */
.profile-forms {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-section {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.section-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.section-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.profile-form {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(44, 85, 48, 0.1);
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--dark-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

/* إعدادات الحساب */
.settings-list {
    padding: 20px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1rem;
}

.setting-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* مفاتيح التبديل */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* منطقة الخطر */
.danger-zone {
    border: 2px solid #dc3545;
}

.danger-zone .section-header {
    background: #f8d7da;
    color: #721c24;
}

.danger-actions {
    padding: 20px;
    text-align: center;
}

.danger-warning {
    margin-top: 15px;
    color: #721c24;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .profile-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .profile-card {
        position: static;
    }
    
    .profile-header {
        padding: 20px;
    }
    
    .profile-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .profile-info h2 {
        font-size: 1.3rem;
    }
    
    .page-header h1 {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .switch {
        align-self: flex-end;
    }
}
