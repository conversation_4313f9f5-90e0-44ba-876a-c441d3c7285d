# تحديث صلاحيات إضافة الخطب والمحاضرات

## نظرة عامة
تم تطبيق نظام صلاحيات شامل لإضافة الخطب الجاهزة والمحاضرات والدروس بحيث يُسمح فقط للمستخدمين المؤهلين بإضافة محتوى جديد.

## الأدوار المسموح لها بإضافة المحتوى
1. **مشرف المنصة (admin)** - صلاحية كاملة لإضافة الخطب والمحاضرات
2. **العالم (scholar)** - يمكنه إضافة الخطب والمحاضرات والإجابة على الفتاوى
3. **الخطيب (member)** - يمكنه إضافة الخطب والمحاضرات

## التغييرات المطبقة

### 1. تحديث الخادم (Backend)

#### أ. الخطب (`routes/sermons.js`)
- إضافة التحقق من الدور قبل السماح بإنشاء خطبة جديدة
- رفض الطلبات من المستخدمين غير المؤهلين مع رسالة خطأ واضحة

#### ب. المحاضرات (`routes/lectures.js`)
- إضافة التحقق من الدور قبل السماح بإنشاء محاضرة جديدة
- تطبيق نفس نظام الصلاحيات المستخدم في الخطب
- المشرف ينشر مباشرة، الآخرون يحتاجون موافقة

### 2. تحديث واجهة المستخدم (Frontend)

#### أ. صفحة الخطب الجاهزة (`public/sermons.html`)
- تقسيم قسم إضافة الخطبة إلى ثلاث حالات:
  - **للمستخدمين المؤهلين:** عرض زر إضافة خطبة
  - **للمستخدمين غير المؤهلين:** عرض رسالة توضيحية
  - **للزوار:** عرض أزرار تسجيل الدخول

#### ب. صفحة إضافة الخطبة (`public/add_sermon.html`)
- إضافة حماية للصفحة بالكامل
- منع الوصول للمستخدمين غير المؤهلين
- عرض رسالة خطأ واضحة للمستخدمين غير المؤهلين

### 3. تحديث JavaScript

#### أ. ملف الخطب (`public/js/sermons.js`)
- إضافة دالة `manageAddSermonSection()` لإدارة عرض قسم إضافة الخطبة
- التحقق من دور المستخدم وعرض المحتوى المناسب

#### ب. ملف إضافة الخطبة (`public/js/add-sermon.js`)
- إضافة دالة `checkSermonAddPermission()` للتحقق من الصلاحيات
- إضافة دالة `showAccessDeniedPage()` لعرض صفحة منع الوصول

#### ج. ملف المحاضرات (`public/js/lectures.js`)
- تحديث دالة `manageLectureAddPermissions()` لإدارة عرض زر إضافة المحاضرة
- إضافة قسم معلومات توضيحي للمستخدمين غير المؤهلين
- تطبيق نفس نظام التحقق من الصلاحيات المستخدم في الخطب

### 4. تحديث نظام الأذونات (`public/js/auth-protection.js`)
- تحديث أسماء الأدوار لتكون أكثر وضوحاً:
  - `admin`: مشرف المنصة
  - `scholar`: عالم  
  - `member`: خطيب
- إضافة صلاحية `add_sermons` للعلماء والخطباء

### 5. تحديث الأنماط

#### أ. الأنماط العامة (`public/style.css`)
- إضافة أنماط لرسالة عدم وجود صلاحية
- تحسين مظهر رسائل التنبيه

#### ب. أنماط المحاضرات (`public/css/lectures.css`)
- إضافة أنماط لقسم معلومات إضافة المحاضرات
- تنسيقات متجاوبة للقسم الجديد

## كيفية الاختبار

### 1. استخدام ملف الاختبار
افتح الملف `test_sermon_permissions.html` في المتصفح لاختبار الصلاحيات:
- جرب تسجيل الدخول بأدوار مختلفة
- اختبر صلاحية إضافة الخطبة لكل دور
- تصفح صفحات الخطب وإضافة الخطبة

### 2. اختبار يدوي
1. **كزائر:** تصفح صفحة الخطب - يجب أن ترى أزرار تسجيل الدخول
2. **كخطيب:** سجل دخول كعضو - يجب أن ترى أزرار إضافة الخطب والمحاضرات
3. **كعالم:** سجل دخول كعالم - يجب أن ترى أزرار إضافة الخطب والمحاضرات
4. **كمشرف:** سجل دخول كمدير - يجب أن ترى أزرار إضافة الخطب والمحاضرات

## الملفات المعدلة

### الخطب
- `routes/sermons.js`
- `public/sermons.html`
- `public/add_sermon.html`
- `public/js/sermons.js`
- `public/js/add-sermon.js`

### المحاضرات
- `routes/lectures.js`
- `public/lectures.html`
- `public/js/lectures.js`
- `public/css/lectures.css`

### الملفات المشتركة
- `public/js/auth-protection.js`
- `public/style.css`

## ملفات الاختبار المضافة
- `test_sermon_permissions.html` - ملف اختبار تفاعلي
- `SERMON_PERMISSIONS_README.md` - هذا الملف

## ملاحظات مهمة
1. تأكد من أن نظام المصادقة يعمل بشكل صحيح
2. تأكد من تحديث قاعدة البيانات لتتضمن الأدوار الصحيحة
3. اختبر جميع السيناريوهات قبل النشر في الإنتاج
4. تأكد من أن رسائل الخطأ واضحة ومفيدة للمستخدمين

## التحسينات المستقبلية المقترحة
1. إضافة نظام طلب ترقية الدور للمستخدمين
2. إضافة نظام موافقة على الخطب قبل النشر
3. إضافة إحصائيات لمتابعة نشاط المستخدمين
4. تحسين واجهة إدارة الأذونات
