version: '3.8'

services:
  tamsik-app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_TYPE=sqlite
      - JWT_SECRET=tamsik_jwt_secret_key_2024_production
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: MySQL service (uncomment if you want to use MySQL)
  # mysql:
  #   image: mysql:8.0
  #   environment:
  #     MYSQL_ROOT_PASSWORD: your_root_password
  #     MYSQL_DATABASE: tamsik_db
  #     MYSQL_USER: tamsik_user
  #     MYSQL_PASSWORD: tamsik_password
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   restart: unless-stopped

volumes:
  mysql_data: 