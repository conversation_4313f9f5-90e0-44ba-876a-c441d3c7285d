/* ألوان أساسية متناسقة مع هوية الموقع الإسلامي */
:root {
    --primary-color: #1d8a4e;     /* أخضر إسلامي */
    --secondary-color: #d4af37;   /* ذهبي */
    --dark-color: #1a3a4a;        /* أزرق داكن */
    --light-color: #f8f9fa;       /* أبيض فاتح */
    --accent-color: #c75c5c;      /* أحمر طوبي */
    --text-color: #333;           /* لون النص الأساسي */
    --text-light: #f8f9fa;        /* لون النص الفاتح */
    --border-color: #e0e0e0;      /* لون الحدود */
    --bg-light: #f5f5f5;          /* خلفية فاتحة */
    --bg-dark: #0a2e38;           /* خلفية داكنة */
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>', 'Scheherazade New', serif;
    color: var(--text-color);
    line-height: 1.6;
    background-color: var(--light-color);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

a:hover {
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-light);
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: transparent;
    color: var(--primary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: 2px solid var(--secondary-color);
}

.btn-secondary:hover {
    background-color: transparent;
    color: var(--secondary-color);
}

/* صفحة من نحن */
.about-section {
    padding: 2rem 0;
}

.about-intro {
    text-align: center;
    margin-bottom: 3rem;
}

.about-intro .lead {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto;
}

.mission-vision {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.mission, .vision {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    border-right: 4px solid var(--primary-color);
}

.mission h3, .vision h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.values-list {
    display: grid;
    gap: 1rem;
    margin: 2rem 0;
}

.value-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.value-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-top: 0.2rem;
}

.value-item h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.contact-cta {
    text-align: center;
    margin-top: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: white;
    border-radius: 10px;
}

.contact-cta h3 {
    color: white;
    margin-bottom: 1rem;
}

/* صفحة اتصل بنا */
.contact-section {
    padding: 2rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

.contact-methods {
    display: grid;
    gap: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.contact-method i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: 0.5rem;
}

.contact-method h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.social-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: var(--primary-color);
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.contact-form .form-group {
    margin-bottom: 1.5rem;
}

.contact-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.contact-form input,
.contact-form select,
.contact-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 3px;
    position: relative;
}

input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
}

.btn-block {
    width: 100%;
    display: block;
}

/* الأسئلة الشائعة */
.faq-section {
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 1px solid #eee;
}

.faq-list {
    display: grid;
    gap: 1rem;
}

.faq-item {
    border: 1px solid #eee;
    border-radius: 8px;
    overflow: hidden;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background: #e9ecef;
}

.faq-question h3 {
    margin: 0;
    color: #333;
    font-size: 1.1rem;
}

.faq-question i {
    color: var(--primary-color);
    transition: transform 0.3s ease;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer p {
    padding: 1.5rem;
    margin: 0;
    color: #666;
    line-height: 1.6;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .mission-vision {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}