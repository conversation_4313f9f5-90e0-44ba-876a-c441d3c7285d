<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - تمسيك</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/error-handler.css">
    <link rel="stylesheet" href="css/profile.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل -->
    <header>
        <nav class="navbar">
            <div class="container">
                <a href="index.html" class="logo">تمسيك</a>
                <p class="slogan">"والذين يمسكون بالكتاب..."</p>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sermons.html"><i class="fas fa-book-open"></i> الخطب الجاهزة</a></li>
                    <li><a href="prepare_sermon.html" data-auth-required="member"><i class="fas fa-pen"></i> إعداد خطبة</a></li>
                    <li><a href="scholars.html"><i class="fas fa-user-graduate"></i> العلماء اليمنيين</a></li>
                    <li><a href="thinkers.html"><i class="fas fa-lightbulb"></i> المفكرون والدعاة</a></li>
                    <li><a href="lectures.html"><i class="fas fa-play-circle"></i> المحاضرات</a></li>
                    <li class="auth-links">
                        <a href="profile.html" class="user-profile" data-auth-required="member">
                            <i class="fas fa-user"></i>
                            <span id="user-name">الملف الشخصي</span>
                        </a>
                        <a href="admin.html" class="admin-link" data-auth-required="admin" style="display: none;">
                            <i class="fas fa-cogs"></i>
                            لوحة الإدارة
                        </a>
                        <a href="#" class="logout-link" onclick="logout()" data-auth-required="member">
                            <i class="fas fa-sign-out-alt"></i>
                            تسجيل الخروج
                        </a>
                        <a href="login.html" class="login-link" data-auth-required="guest">
                            <i class="fas fa-sign-in-alt"></i>
                            تسجيل الدخول
                        </a>
                    </li>
                </ul>
                <button class="mobile-menu-toggle"><i class="fas fa-bars"></i></button>
            </div>
        </nav>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="container">
            <!-- رأس الصفحة -->
            <div class="page-header">
                <h1>
                    <i class="fas fa-user"></i>
                    الملف الشخصي
                </h1>
                <p>إدارة بياناتك الشخصية وإعداداتك</p>
            </div>

            <!-- محتوى الملف الشخصي -->
            <div class="profile-content">
                <!-- معلومات المستخدم -->
                <div class="profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="profile-info">
                            <h2 id="profile-name">اسم المستخدم</h2>
                            <p id="profile-email">البريد الإلكتروني</p>
                            <span id="profile-role" class="role-badge">الدور</span>
                        </div>
                    </div>
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <i class="fas fa-calendar-alt"></i>
                            <div>
                                <span>تاريخ التسجيل</span>
                                <strong id="profile-join-date">--</strong>
                            </div>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <span>آخر تحديث</span>
                                <strong id="profile-last-update">--</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نماذج التحرير -->
                <div class="profile-forms">
                    <!-- تحرير المعلومات الشخصية -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3>
                                <i class="fas fa-edit"></i>
                                تحرير المعلومات الشخصية
                            </h3>
                        </div>
                        <form id="profile-form" class="profile-form">
                            <div class="form-group">
                                <label for="edit-name">الاسم:</label>
                                <input type="text" id="edit-name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-email">البريد الإلكتروني:</label>
                                <input type="email" id="edit-email" name="email" required>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i>
                                    حفظ التغييرات
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetProfileForm()">
                                    <i class="fas fa-undo"></i>
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- تغيير كلمة المرور -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3>
                                <i class="fas fa-lock"></i>
                                تغيير كلمة المرور
                            </h3>
                        </div>
                        <form id="password-form" class="profile-form">
                            <div class="form-group">
                                <label for="current-password">كلمة المرور الحالية:</label>
                                <input type="password" id="current-password" name="currentPassword" required>
                            </div>
                            <div class="form-group">
                                <label for="new-password">كلمة المرور الجديدة:</label>
                                <input type="password" id="new-password" name="newPassword" required minlength="6">
                            </div>
                            <div class="form-group">
                                <label for="confirm-password">تأكيد كلمة المرور الجديدة:</label>
                                <input type="password" id="confirm-password" name="confirmPassword" required minlength="6">
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key"></i>
                                    تغيير كلمة المرور
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetPasswordForm()">
                                    <i class="fas fa-undo"></i>
                                    إلغاء
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- إعدادات الحساب -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3>
                                <i class="fas fa-cog"></i>
                                إعدادات الحساب
                            </h3>
                        </div>
                        <div class="settings-list">
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>الإشعارات</h4>
                                    <p>تلقي إشعارات عن المحتوى الجديد</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" id="notifications-toggle">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>الخصوصية</h4>
                                    <p>إخفاء ملفك الشخصي عن المستخدمين الآخرين</p>
                                </div>
                                <label class="switch">
                                    <input type="checkbox" id="privacy-toggle">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- منطقة الخطر -->
                    <div class="form-section danger-zone">
                        <div class="section-header">
                            <h3>
                                <i class="fas fa-exclamation-triangle"></i>
                                منطقة الخطر
                            </h3>
                        </div>
                        <div class="danger-actions">
                            <button class="btn btn-danger" onclick="deleteAccount()">
                                <i class="fas fa-trash"></i>
                                حذف الحساب نهائياً
                            </button>
                            <p class="danger-warning">
                                <i class="fas fa-warning"></i>
                                تحذير: هذا الإجراء لا يمكن التراجع عنه
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- الفوتر -->
    <footer>
        <div class="footer-content">
            <div class="container">
                <p>&copy; 2025 تمسيك. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- تحميل الملفات -->
    <script src="js/auth-protection.js"></script>
    <script src="js/error-handler.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/profile.js"></script>
</body>
</html>
