/* تنسيقات صفحة المحاضرات والدروس */

/* عنوان الصفحة */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 30px 0;
    border-bottom: 2px solid var(--border-color);
}

.page-title-section {
    flex: 1;
}

.page-title {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-title i {
    color: var(--secondary-color);
}

.page-description {
    font-size: 1.1rem;
    /* color: var(--text-color); */
    color: azure;
    /* opacity: 0.8; */
    max-width: 600px;
}

.page-actions {
    display: flex;
    gap: 10px;
}

/* شريط البحث والفلترة */
.search-filter-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 1.1rem;
}

.search-box input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(29, 138, 78, 0.1);
}

.filter-controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.filter-controls select {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.filter-controls select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* أزرار العرض */
.view-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
    justify-content: center;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.view-btn {
    padding: 10px 20px;
    border: 2px solid var(--border-color);
    background: white;
    color: var(--text-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
}

.view-btn:hover {
    background: var(--bg-light);
    border-color: var(--primary-color);
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* العرض الجدولي */
.lectures-table-view {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.table-container {
    overflow-x: auto;
}

.lectures-table {
    width: 100%;
    border-collapse: collapse;
}

.lectures-table th {
    background: var(--primary-color);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
    white-space: nowrap;
}

.lectures-table th:hover {
    background: var(--dark-color);
}

.lectures-table th i {
    margin-right: 5px;
    opacity: 0.7;
}

.lectures-table td {
    padding: 15px 12px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.lectures-table tr:hover {
    background: var(--bg-light);
}

.lectures-table tr:last-child td {
    border-bottom: none;
}

/* أزرار الإجراءات في الجدول */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.action-btn.view {
    background: var(--primary-color);
    color: white;
}

.action-btn.edit {
    background: var(--secondary-color);
    color: white;
}

.action-btn.delete {
    background: var(--accent-color);
    color: white;
}

.action-btn:hover {
    transform: scale(1.05);
}

/* عرض البطاقات */
.lectures-cards-view {
    margin-bottom: 30px;
}

.lectures-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
}

.lecture-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.lecture-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.lecture-card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: white;
    padding: 20px;
    position: relative;
}

.lecture-type {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--secondary-color);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.lecture-title {
    font-size: 1.3rem;
    font-weight: bold;
    margin-bottom: 10px;
    line-height: 1.4;
}

.lecture-lecturer {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    opacity: 0.9;
}

.lecture-card-body {
    padding: 20px;
}

.lecture-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-color);
}

.info-item i {
    color: var(--primary-color);
    width: 16px;
}

.lecture-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.lecture-card-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    font-size: 0.8rem;
    color: #666;
}

.card-actions {
    display: flex;
    gap: 8px;
}

/* عرض التقويم */
.calendar-view {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.calendar-nav {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.calendar-nav:hover {
    background: var(--dark-color);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.calendar-day {
    background: white;
    padding: 15px 10px;
    min-height: 100px;
    position: relative;
}

.calendar-day.other-month {
    background: var(--bg-light);
    color: #999;
}

.calendar-day.today {
    background: var(--primary-color);
    color: white;
}

.day-number {
    font-weight: bold;
    margin-bottom: 5px;
}

.day-events {
    font-size: 0.7rem;
}

.event-item {
    background: var(--secondary-color);
    color: white;
    padding: 2px 5px;
    border-radius: 3px;
    margin-bottom: 2px;
    cursor: pointer;
}

/* رسالة عدم وجود نتائج */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #666;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.no-results i {
    font-size: 4rem;
    color: var(--border-color);
    margin-bottom: 20px;
}

.no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--text-color);
}

/* الترقيم */
.pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.pagination {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination button {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    background: white;
    color: var(--text-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
}

.pagination button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination button:disabled:hover {
    background: white;
    color: var(--text-color);
    border-color: var(--border-color);
}

/* النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    overflow-y: auto;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: var(--bg-light);
    color: var(--primary-color);
}

.modal-body {
    padding: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(29, 138, 78, 0.1);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    margin-top: 20px;
}

/* تنسيقات إضافية */
.lecture-type-badge {
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    color: white;
}

.lecture-type-badge.محاضرة {
    background: var(--primary-color);
}

.lecture-type-badge.درس {
    background: var(--secondary-color);
}

.lecture-type-badge.ندوة {
    background: var(--accent-color);
}

.lecture-type-badge.دورة {
    background: var(--dark-color);
}

.calendar-day-header {
    background: var(--primary-color);
    color: white;
    padding: 10px;
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
}

/* التجاوب */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }

    .search-filter-section {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        min-width: auto;
    }

    .filter-controls {
        justify-content: stretch;
    }

    .filter-controls select {
        flex: 1;
        min-width: auto;
    }

    .view-controls {
        flex-wrap: wrap;
    }

    .table-container {
        font-size: 0.8rem;
    }

    .lectures-table th,
    .lectures-table td {
        padding: 8px 6px;
    }

    .lectures-grid {
        grid-template-columns: 1fr;
    }

    .lecture-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .lecture-card-footer {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }

    .calendar-grid {
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
    }

    .calendar-day {
        padding: 8px 5px;
        min-height: 60px;
    }

    .modal-content {
        margin: 10px;
        max-height: calc(100vh - 20px);
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
}

/* قسم معلومات إضافة المحاضرات */
.lecture-add-info {
    margin-bottom: 25px;
}

.info-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-content {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.info-content i {
    font-size: 2rem;
    color: #17a2b8;
}

.info-text h4 {
    margin: 0 0 5px 0;
    color: #495057;
    font-size: 1.1rem;
}

.info-text p {
    margin: 0;
    color: #6c757d;
    font-size: 0.95rem;
}

.info-actions {
    display: flex;
    gap: 10px;
}

.info-actions .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* تنسيقات متجاوبة للقسم الجديد */
@media (max-width: 768px) {
    .info-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .info-content {
        flex-direction: column;
        text-align: center;
    }

    .info-actions {
        justify-content: center;
        flex-wrap: wrap;
    }
}
