<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تمسيك - اتصل بنا</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/auth-protection.css">

    <!-- الخطوط -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="container">
                <a href="index.html" class="logo">تمسيك</a>
                <p class="slogan">"والذين يمسكون بالكتاب..."</p>
                <button class="mobile-menu-toggle"><i class="fas fa-bars"></i></button>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="sermons.html"><i class="fas fa-book-open"></i> الخطب الجاهزة</a></li>
                    <li><a href="prepare_sermon.html" data-auth-required="member"><i class="fas fa-pen"></i> إعداد خطبة</a></li>
                    <li><a href="scholars.html"><i class="fas fa-user-graduate"></i> العلماء اليمنيين</a></li>
                    <li><a href="thinkers.html"><i class="fas fa-lightbulb"></i> المفكرون والدعاة</a></li>
                    <li><a href="lectures.html"><i class="fas fa-microphone"></i> المحاضرات والدروس</a></li>
                </ul>
                
                <!-- معلومات المستخدم -->
                <div class="user-info" data-auth-only style="display: none;">
                    <span data-user-name></span>
                    <span class="user-role-badge" data-user-role></span>
                    <button class="btn-logout" onclick="window.authProtection?.logout()">
                        <i class="fas fa-sign-out-alt"></i> خروج
                    </button>
                </div>
                
                <!-- أزرار تسجيل الدخول -->
                <div class="auth-buttons" data-guest-only>
                    <a href="login.html" class="btn btn-outline">تسجيل الدخول</a>
                    <a href="register.html" class="btn btn-primary">إنشاء حساب</a>
                </div>
            </div>
        </nav>
    </header>

    <div class="page-header">
        <div class="container">
            <h1>اتصل بنا</h1>
            <p>نحن هنا للإجابة على استفساراتكم ومساعدتكم</p>
        </div>
    </div>

    <main class="container page-content">
        <section class="contact-section">
            <div class="contact-content">
                <div class="contact-info">
                    <h2>معلومات التواصل</h2>
                    <div class="contact-methods">
                        <div class="contact-method">
                            <i class="fas fa-envelope"></i>
                            <div>
                                <h3>البريد الإلكتروني</h3>
                                <p><EMAIL></p>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <i class="fas fa-phone"></i>
                            <div>
                                <h3>الهاتف</h3>
                                <p>+967 1 234 567</p>
                                <p>متاح من السبت إلى الخميس (9 صباحاً - 5 مساءً)</p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <h3>العنوان</h3>
                                <p>صنعاء، الجمهورية اليمنية</p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <i class="fas fa-share-alt"></i>
                            <div>
                                <h3>وسائل التواصل الاجتماعي</h3>
                                <div class="social-links">
                                    <a href="#" class="social-link facebook">
                                        <i class="fab fa-facebook"></i> Facebook
                                    </a>
                                    <a href="#" class="social-link twitter">
                                        <i class="fab fa-twitter"></i> Twitter
                                    </a>
                                    <a href="#" class="social-link instagram">
                                        <i class="fab fa-instagram"></i> Instagram
                                    </a>
                                    <a href="#" class="social-link youtube">
                                        <i class="fab fa-youtube"></i> YouTube
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="contact-form-section">
                    <h2>أرسل لنا رسالة</h2>
                    <form class="contact-form" id="contact-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="name">الاسم الكامل</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">الموضوع</label>
                            <select id="subject" name="subject" required>
                                <option value="">اختر الموضوع</option>
                                <option value="general">استفسار عام</option>
                                <option value="technical">مشكلة تقنية</option>
                                <option value="content">اقتراح محتوى</option>
                                <option value="partnership">شراكة</option>
                                <option value="feedback">ملاحظات وتقييم</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">الرسالة</label>
                            <textarea id="message" name="message" rows="6" placeholder="اكتب رسالتك هنا..." required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="newsletter" name="newsletter">
                                <span class="checkmark"></span>
                                أرغب في الاشتراك في النشرة البريدية
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-paper-plane"></i> إرسال الرسالة
                        </button>
                    </form>
                </div>
            </div>
        </section>

        <section class="faq-section">
            <h2>الأسئلة الشائعة</h2>
            <div class="faq-list">
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>كيف يمكنني إنشاء حساب في المنصة؟</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>يمكنك إنشاء حساب جديد من خلال النقر على زر "إنشاء حساب" في أعلى الصفحة وملء البيانات المطلوبة.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>هل المحتوى مجاني؟</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>نعم، معظم المحتوى في المنصة مجاني. بعض الخدمات المتقدمة قد تتطلب اشتراكاً.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>كيف يمكنني المساهمة في المحتوى؟</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>يمكنك المساهمة من خلال إرسال المحتوى الإسلامي الموثوق عبر نموذج التواصل أو البريد الإلكتروني.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">
                        <h3>كيف أتأكد من صحة المحتوى؟</h3>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>جميع المحتوى يتم مراجعته من قبل متخصصين في العلوم الشرعية قبل النشر.</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-about">
                    <h3>عن تمسيك</h3>
                    <p>منصة إسلامية شاملة تهدف إلى مساعدة الخطباء والباحثين وعامة المسلمين في الوصول إلى محتوى إسلامي موثوق.</p>
                </div>
                <div class="footer-links">
                    <h3>روابط سريعة</h3>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="sermons.html">الخطب الجاهزة</a></li>
                        <li><a href="prepare_sermon.html">إعداد خطبة</a></li>
                        <li><a href="about.html">من نحن</a></li>
                        <li><a href="contact.html">اتصل بنا</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>تواصل معنا</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 تمسيك. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="js/auth.js"></script>
    <script src="js/simple-auth.js"></script>
    <script src="js/auth-protection.js"></script>
    <script src="js/main.js"></script>

    <script>
        // معالجة نموذج التواصل
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            submitButton.disabled = true;
            
            // محاكاة إرسال الرسالة
            setTimeout(() => {
                alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
                this.reset();
                
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }, 2000);
        });

        // معالجة الأسئلة الشائعة
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', function() {
                const faqItem = this.parentElement;
                const answer = faqItem.querySelector('.faq-answer');
                const icon = this.querySelector('i');
                
                faqItem.classList.toggle('active');
                
                if (faqItem.classList.contains('active')) {
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                    icon.style.transform = 'rotate(180deg)';
                } else {
                    answer.style.maxHeight = '0';
                    icon.style.transform = 'rotate(0deg)';
                }
            });
        });
    </script>
</body>
</html>
