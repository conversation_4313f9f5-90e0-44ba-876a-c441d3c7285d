# دليل إعداد صفحة الخطب المحسنة - تمسيك

## نظرة عامة

تم تطوير صفحة إعداد الخطب بشكل شامل لتشمل جميع عناصر الخطبة الإسلامية التقليدية مع نظام اقتراحات تفاعلي يعتمد على مساهمات المستخدمين السابقين.

## الميزات الجديدة

### 1. البنية المنظمة للخطبة
- **العنوان الرئيسي**: حقل لإدخال عنوان الخطبة
- **المقدمة**: تشمل:
  - الأثر (آية قرآنية أو حديث أو قول)
  - السجع (الموضوع، القافية، النسبة، المرجع)
  - الشعر (الموضوع، القافية، البحر، الشاعر، المرجع)
- **أما بعد**: يحتوي على:
  - الوصية بالتقوى مع آيات وأحاديث وآثار مصنفة حسب النوع
- **نص الخطبة**: المحتوى الرئيسي
- **خاتمة الخطبة الأولى**
- **الخطبة الثانية**
- **خاتمة الخطبة الثانية**: تشمل:
  - الصلاة على النبي
  - أنواع مختلفة من الدعاء
  - إمكانية إضافة عناصر إضافية

### 2. نظام الاقتراحات التفاعلي
- **حفظ تلقائي**: كل ما يدخله المستخدم يُحفظ في قاعدة البيانات
- **اقتراحات ذكية**: عرض اقتراحات من المستخدمين السابقين
- **تصنيف حسب الاستخدام**: الاقتراحات الأكثر استخداماً تظهر أولاً
- **بحث متقدم**: إمكانية البحث في الاقتراحات
- **تصنيف حسب النوع**: آيات، أحاديث، شعر، سجع، أثار، دعاء

### 3. العناصر الإضافية
- إمكانية إضافة آيات قرآنية
- إمكانية إضافة أحاديث مع التخريج
- إمكانية إضافة شعر
- إمكانية إضافة أقوال وآثار
- إمكانية إضافة قصص

## متطلبات التشغيل

### 1. قاعدة البيانات
```bash
# تثبيت MySQL
# Windows: تحميل من https://dev.mysql.com/downloads/mysql/
# Ubuntu/Debian:
sudo apt update
sudo apt install mysql-server

# macOS:
brew install mysql
```

### 2. إعداد قاعدة البيانات
```bash
# تشغيل MySQL
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS

# إنشاء قاعدة البيانات والجداول
node config/initDatabase.js
```

### 3. متغيرات البيئة
إنشاء ملف `.env` في الجذر:
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=tamsik_db
DB_PORT=3306
```

## الملفات المحدثة

### 1. قاعدة البيانات
- `config/createTables.js`: إضافة جداول الاقتراحات الجديدة
- `config/initDatabase.js`: ملف تهيئة قاعدة البيانات
- `models/SermonSuggestions.js`: نموذج إدارة الاقتراحات
- `models/Sermon.js`: تحديث نموذج الخطبة لدعم البنية الجديدة

### 2. واجهة المستخدم
- `public/prepare_sermon.html`: إعادة تصميم كاملة للصفحة
- `public/css/prepare-sermon.css`: أنماط جديدة للتصميم المحسن
- `public/js/prepare-sermon.js`: JavaScript محدث لنظام الاقتراحات

### 3. الخادم
- `routes/suggestions.js`: API endpoints للاقتراحات
- `server.js`: إضافة route الاقتراحات

## كيفية الاستخدام

### 1. تشغيل الخادم
```bash
npm start
```

### 2. الوصول للصفحة
```
http://localhost:3000/prepare_sermon.html
```

### 3. استخدام الاقتراحات
1. انقر على زر "اقتراحات" بجانب أي حقل
2. ستظهر نافذة بالاقتراحات المتاحة
3. اختر الاقتراح المناسب
4. انقر "استخدام هذا الاقتراح"

### 4. إضافة عناصر إضافية
1. في قسم "خاتمة الخطبة الثانية"
2. انقر على أحد أزرار "إضافة"
3. املأ البيانات المطلوبة
4. يمكن حذف العنصر بالنقر على زر الحذف

## جداول قاعدة البيانات الجديدة

### 1. verses_suggestions
- حفظ اقتراحات الآيات القرآنية
- تصنيف حسب النوع (إخبار، أمر، وعد)

### 2. hadith_suggestions
- حفظ اقتراحات الأحاديث
- تشمل الراوي والمصدر والتخريج

### 3. athar_suggestions
- حفظ اقتراحات الآثار والأقوال
- تصنيف حسب النوع (إخبار، قصة)

### 4. saja_suggestions
- حفظ اقتراحات السجع
- تشمل القافية والنسبة والمرجع

### 5. poetry_suggestions
- حفظ اقتراحات الشعر
- تشمل البحر والشاعر والمرجع

### 6. dua_suggestions
- حفظ اقتراحات الدعاء
- تصنيف حسب النوع (ثناء، قرآني، نبوي، إلخ)

## الميزات التقنية

### 1. الحفظ التلقائي
- حفظ في التخزين المحلي كل 30 ثانية
- حفظ عند تغيير المحتوى مع تأخير 2 ثانية

### 2. البحث والتصفية
- بحث في الاقتراحات حسب النص
- تصفية حسب النوع والسياق
- ترتيب حسب عدد الاستخدام

### 3. واجهة مستخدم محسنة
- تصميم متجاوب مع جميع الأجهزة
- أيقونات واضحة لكل قسم
- ألوان متناسقة مع هوية الموقع

## استكشاف الأخطاء

### 1. خطأ الاتصال بقاعدة البيانات
```
Error: ECONNREFUSED
```
**الحل**: تأكد من تشغيل MySQL وصحة إعدادات الاتصال

### 2. خطأ في الجداول
```
Error: Table doesn't exist
```
**الحل**: تشغيل `node config/initDatabase.js`

### 3. مشاكل في الاقتراحات
- تحقق من وجود البيانات في الجداول
- تأكد من صحة API endpoints
- فحص console للأخطاء

## التطوير المستقبلي

### 1. ميزات مقترحة
- تصدير إلى Word مع تنسيق عربي
- نظام تقييم للاقتراحات
- إمكانية مشاركة الخطب
- نظام إشعارات للاقتراحات الجديدة

### 2. تحسينات تقنية
- تحسين أداء البحث
- إضافة فهرسة للنصوص العربية
- تحسين واجهة المستخدم
- إضافة اختبارات تلقائية

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
