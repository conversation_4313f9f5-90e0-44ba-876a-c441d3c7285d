/**
 * أنماط معالجة الأخطاء
 */

/* حاوي الأخطاء */
.error-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    pointer-events: none;
}

.error-container > * {
    pointer-events: all;
}

/* أنماط التنبيهات */
.alert {
    padding: 12px 16px;
    margin-bottom: 10px;
    border: 1px solid transparent;
    border-radius: 8px;
    font-size: 14px;
    line-height: 1.4;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    animation: slideIn 0.3s ease-out;
    transition: all 0.3s ease;
}

.alert:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* أنواع التنبيهات */
.alert-error {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border-color: #ff5252;
}

.alert-warning {
    background: linear-gradient(135deg, #ffa726, #ff9800);
    color: white;
    border-color: #ff9800;
}

.alert-info {
    background: linear-gradient(135deg, #42a5f5, #2196f3);
    color: white;
    border-color: #2196f3;
}

.alert-success {
    background: linear-gradient(135deg, #66bb6a, #4caf50);
    color: white;
    border-color: #4caf50;
}

/* زر الإغلاق */
.btn-close {
    background: none;
    border: none;
    color: inherit;
    opacity: 0.7;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.btn-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.2);
}

.btn-close i {
    font-size: 12px;
}

/* أنماط النماذج */
.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.error-message {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 0.25rem;
    display: block;
}

/* رسائل الخطأ في الصفحات */
.page-error {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 12px;
    margin: 20px 0;
}

.page-error i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 20px;
    display: block;
}

.page-error h2 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.page-error p {
    color: #6c757d;
    margin-bottom: 25px;
    font-size: 1rem;
}

.page-error .btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.page-error .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* حالة التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* رسائل فارغة */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 10px;
    font-size: 1.25rem;
}

.empty-state p {
    margin-bottom: 0;
    font-size: 0.95rem;
}

/* الرسوم المتحركة */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* تأثيرات إضافية */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .error-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .alert {
        font-size: 13px;
        padding: 10px 12px;
    }
    
    .page-error {
        padding: 40px 15px;
    }
    
    .page-error i {
        font-size: 3rem;
    }
    
    .page-error h2 {
        font-size: 1.25rem;
    }
}

/* الوضع المظلم */
@media (prefers-color-scheme: dark) {
    .page-error {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .page-error i {
        color: #a0aec0;
    }
    
    .page-error h2 {
        color: #e2e8f0;
    }
    
    .page-error p {
        color: #a0aec0;
    }
    
    .loading-overlay {
        background: rgba(45, 55, 72, 0.9);
    }
    
    .empty-state {
        color: #a0aec0;
    }
}

/* تحسينات إضافية للوصولية */
.alert[role="alert"] {
    position: relative;
}

.alert:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.btn-close:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 1px;
}

/* حالات خاصة */
.network-error {
    border-left: 4px solid #dc3545;
}

.validation-error {
    border-left: 4px solid #ffc107;
}

.auth-error {
    border-left: 4px solid #fd7e14;
}
