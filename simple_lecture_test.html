<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط لزر المحاضرة</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .page-actions {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        h1, h2, h3 { color: #333; }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-microphone"></i> اختبار زر إضافة المحاضرة</h1>
        
        <div class="test-section">
            <h3>محاكاة زر إضافة المحاضرة</h3>
            <div class="page-actions">
                <button id="add-lecture-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    إضافة محاضرة جديدة
                </button>
                <button id="calendar-view-btn" class="btn btn-secondary">
                    <i class="fas fa-calendar"></i>
                    عرض التقويم
                </button>
            </div>
            <div id="button-status" class="status info">
                الزر ظاهر افتراضياً - سيتم التحكم فيه حسب دور المستخدم
            </div>
        </div>
        
        <div class="test-section">
            <h3>إدارة المستخدم التجريبي</h3>
            <button class="btn btn-success" onclick="setUser('admin')">
                <i class="fas fa-user-shield"></i> مشرف المنصة
            </button>
            <button class="btn btn-success" onclick="setUser('scholar')">
                <i class="fas fa-graduation-cap"></i> عالم
            </button>
            <button class="btn btn-success" onclick="setUser('member')">
                <i class="fas fa-microphone"></i> خطيب
            </button>
            <button class="btn btn-warning" onclick="setUser('guest')">
                <i class="fas fa-user"></i> زائر
            </button>
            <button class="btn btn-danger" onclick="setUser(null)">
                <i class="fas fa-sign-out-alt"></i> تسجيل خروج
            </button>
        </div>
        
        <div class="test-section">
            <h3>حالة المستخدم الحالي</h3>
            <div id="user-status" class="status">جاري التحقق...</div>
        </div>
        
        <div class="test-section">
            <h3>اختبار الصلاحيات</h3>
            <button class="btn btn-primary" onclick="testPermissions()">
                <i class="fas fa-check"></i> اختبار الصلاحيات
            </button>
            <div id="permission-result" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>روابط مفيدة</h3>
            <a href="lectures.html" class="btn btn-primary" target="_blank">
                <i class="fas fa-external-link-alt"></i> فتح صفحة المحاضرات الأصلية
            </a>
            <a href="test_lecture_button.html" class="btn btn-secondary" target="_blank">
                <i class="fas fa-tools"></i> اختبار متقدم
            </a>
        </div>
    </div>

    <script>
        let currentUser = null;
        
        // أنواع المستخدمين
        const userTypes = {
            admin: { id: 1, name: 'مشرف المنصة', role: 'admin', email: '<EMAIL>' },
            scholar: { id: 2, name: 'د. محمد العالم', role: 'scholar', email: '<EMAIL>' },
            member: { id: 3, name: 'علي الخطيب', role: 'member', email: '<EMAIL>' },
            guest: { id: 4, name: 'زائر', role: 'guest', email: '<EMAIL>' }
        };
        
        function setUser(type) {
            if (type === null) {
                currentUser = null;
                localStorage.removeItem('currentUser');
                console.log('🚪 تم تسجيل الخروج');
            } else {
                currentUser = userTypes[type];
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                console.log('👤 تم تسجيل الدخول كـ:', currentUser);
            }
            
            updateUserStatus();
            updateButtonVisibility();
        }
        
        function updateUserStatus() {
            const statusDiv = document.getElementById('user-status');
            
            if (currentUser) {
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    <strong>✅ مستخدم مسجل دخول</strong><br>
                    الاسم: ${currentUser.name}<br>
                    الدور: ${currentUser.role}<br>
                    البريد: ${currentUser.email}
                `;
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '<strong>❌ لا يوجد مستخدم مسجل دخول</strong>';
            }
        }
        
        function updateButtonVisibility() {
            const addBtn = document.getElementById('add-lecture-btn');
            const statusDiv = document.getElementById('button-status');
            const allowedRoles = ['admin', 'scholar', 'member'];
            
            if (currentUser && allowedRoles.includes(currentUser.role)) {
                addBtn.style.display = 'inline-block';
                addBtn.className = 'btn btn-success';
                addBtn.innerHTML = `<i class="fas fa-plus"></i> ✅ إضافة محاضرة (${currentUser.role})`;
                
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `✅ الزر ظاهر للدور: ${currentUser.role}`;
            } else if (currentUser) {
                addBtn.style.display = 'inline-block';
                addBtn.className = 'btn btn-danger';
                addBtn.innerHTML = `<i class="fas fa-times"></i> ❌ غير مسموح (${currentUser.role})`;
                
                statusDiv.className = 'status error';
                statusDiv.innerHTML = `❌ الزر مخفي للدور: ${currentUser.role}`;
            } else {
                addBtn.style.display = 'inline-block';
                addBtn.className = 'btn btn-secondary';
                addBtn.innerHTML = `<i class="fas fa-user-slash"></i> يجب تسجيل الدخول`;
                
                statusDiv.className = 'status warning';
                statusDiv.innerHTML = '⚠️ الزر مخفي للزوار - يجب تسجيل الدخول';
            }
        }
        
        function testPermissions() {
            const resultDiv = document.getElementById('permission-result');
            const allowedRoles = ['admin', 'scholar', 'member'];
            
            resultDiv.style.display = 'block';
            
            if (!currentUser) {
                resultDiv.className = 'status error';
                resultDiv.innerHTML = '❌ لا يمكن إضافة محاضرة - يجب تسجيل الدخول أولاً';
            } else if (allowedRoles.includes(currentUser.role)) {
                resultDiv.className = 'status success';
                resultDiv.innerHTML = `✅ ${currentUser.name} لديه صلاحية إضافة المحاضرات`;
            } else {
                resultDiv.className = 'status error';
                resultDiv.innerHTML = `❌ ${currentUser.name} ليس لديه صلاحية إضافة المحاضرات`;
            }
        }
        
        // ربط حدث النقر على زر إضافة المحاضرة
        document.getElementById('add-lecture-btn').addEventListener('click', function() {
            if (currentUser && ['admin', 'scholar', 'member'].includes(currentUser.role)) {
                alert('✅ سيتم فتح نافذة إضافة المحاضرة');
            } else if (currentUser) {
                alert('❌ ليس لديك صلاحية لإضافة محاضرة');
            } else {
                alert('❌ يجب تسجيل الدخول أولاً');
            }
        });
        
        // تحميل المستخدم المحفوظ إن وجد
        const savedUser = localStorage.getItem('currentUser');
        if (savedUser) {
            try {
                currentUser = JSON.parse(savedUser);
            } catch (error) {
                console.error('خطأ في تحميل المستخدم المحفوظ:', error);
            }
        }
        
        // تحديث الواجهة عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateUserStatus();
            updateButtonVisibility();
            console.log('🚀 تم تحميل صفحة الاختبار');
        });
    </script>
</body>
</html>
