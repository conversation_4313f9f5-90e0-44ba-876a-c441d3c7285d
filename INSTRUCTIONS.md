# 📋 تعليمات تشغيل منصة تمسك

## 🎯 الهدف
تم إعداد خادم مبسط لاختبار منصة تمسك الإسلامية بدون الحاجة لإعداد قاعدة بيانات معقد.

---

## ✅ ما تم إنجازه

### 1. 🚀 خادم مبسط للاختبار
- ملف `server-simple.js` للتشغيل السريع
- لا يتطلب قاعدة بيانات للاختبار الأساسي
- يحتوي على API endpoints للاختبار

### 2. 🧪 صفحة اختبار تفاعلية
- ملف `public/test-api.html`
- واجهة سهلة لاختبار جميع الوظائف
- مؤشرات حالة مباشرة
- اختبار المصادقة والاتصال

### 3. 📚 ملفات التوثيق
- `QUICK_START.md` - دليل البدء السريع
- `MYSQL_SETUP.md` - دليل تثبيت MySQL
- `README.md` محدث بتعليمات شاملة

### 4. ⚙️ تحسينات النظام
- إصلاح إعدادات قاعدة البيانات
- رسائل خطأ واضحة ومفيدة
- scripts جديدة في package.json

---

## 🚀 كيفية التشغيل

### الطريقة السريعة (بدون قاعدة بيانات)
```bash
# 1. تشغيل الخادم
node server-simple.js

# 2. فتح المتصفح
# انتقل إلى: http://localhost:3000/test-api.html
```

### الطريقة الكاملة (مع قاعدة البيانات)
```bash
# 1. تثبيت MySQL (راجع MYSQL_SETUP.md)
# 2. تحديث ملف .env بإعدادات MySQL
# 3. تشغيل الخادم
node server-simple.js

# 4. إعداد قاعدة البيانات من صفحة الاختبار
# أو استخدام: npm run setup-db
```

---

## 🔗 الروابط المهمة

| الصفحة | الرابط | الوصف |
|---------|---------|--------|
| **صفحة الاختبار** | http://localhost:3000/test-api.html | اختبار شامل للنظام |
| **الصفحة الرئيسية** | http://localhost:3000 | الموقع الرئيسي |
| **اختبار الصحة** | http://localhost:3000/api/health | حالة الخادم |
| **اختبار قاعدة البيانات** | http://localhost:3000/api/test-db | حالة قاعدة البيانات |

---

## 🧪 اختبارات متاحة

### 1. ✅ اختبار صحة الخادم
- يتحقق من عمل الخادم بشكل طبيعي
- يعرض معلومات النسخة والوقت

### 2. 🗄️ اختبار قاعدة البيانات
- يتحقق من الاتصال بـ MySQL
- يعرض حالة الاتصال (متصل/غير متصل)

### 3. ⚙️ إعداد قاعدة البيانات
- ينشئ قاعدة البيانات والجداول
- يدرج البيانات الأولية
- ينشئ مستخدم إداري افتراضي

### 4. 🔐 اختبار المصادقة
- يختبر تسجيل الدخول
- البيانات الافتراضية:
  - البريد: `<EMAIL>`
  - كلمة المرور: `admin123`

---

## 📱 الصفحات المتاحة

- **العلماء**: http://localhost:3000/scholars.html
- **المفكرين**: http://localhost:3000/thinkers.html
- **الخطب**: http://localhost:3000/sermons.html
- **المحاضرات**: http://localhost:3000/lectures.html
- **تسجيل الدخول**: http://localhost:3000/login.html

---

## 🔧 أوامر مفيدة

```bash
# تشغيل الخادم المبسط
npm run test-server

# تشغيل الخادم العادي
npm start

# تشغيل مع إعادة التحميل التلقائي
npm run dev

# إعداد قاعدة البيانات
npm run setup-db
```

---

## 📋 الخطوات التالية المقترحة

### 1. ✅ مكتمل
- [x] إعداد الخادم المبسط
- [x] صفحة اختبار تفاعلية
- [x] اختبار API endpoints
- [x] توثيق شامل

### 2. 🔄 قيد التطوير
- [ ] ربط Frontend مع Backend APIs
- [ ] إضافة رفع الملفات
- [ ] تطوير لوحة التحكم الإدارية
- [ ] نظام الإشعارات

### 3. 🚀 مستقبلي
- [ ] تحسين الأداء والتخزين المؤقت
- [ ] إضافة المزيد من الميزات
- [ ] اختبارات تلقائية
- [ ] نشر على الخادم

---

## ❗ ملاحظات مهمة

1. **الخادم المبسط** مخصص للاختبار فقط
2. **قاعدة البيانات** اختيارية للاختبار الأساسي
3. **البيانات الافتراضية** للاختبار فقط
4. **تأكد من تشغيل الخادم** قبل فتح صفحة الاختبار

---

## 📞 الدعم

إذا واجهت أي مشاكل:
1. راجع ملف `QUICK_START.md`
2. تحقق من ملف `MYSQL_SETUP.md` لإعداد قاعدة البيانات
3. راجع رسائل الخطأ في وحدة التحكم
4. تأكد من تثبيت Node.js و npm

---

**🎉 مبروك! منصة تمسك جاهزة للاختبار**

انتقل إلى http://localhost:3000/test-api.html وابدأ الاختبار! 🚀
