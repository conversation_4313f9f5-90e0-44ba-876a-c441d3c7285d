/* تنسيقات صفحة تفاصيل المقال */

/* شريط التنقل */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 30px;
    padding: 15px 0;
    font-size: 0.9rem;
    color: #666;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: var(--secondary-color);
}

.breadcrumb i {
    font-size: 0.8rem;
    color: #999;
}

/* حاوية المقال */
.article-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.article-header {
    position: relative;
    padding: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: white;
}

.article-category {
    display: inline-block;
    background: var(--secondary-color);
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    margin-bottom: 15px;
}

.article-title {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1.3;
    margin-bottom: 20px;
}

.article-meta {
    display: flex;
    gap: 25px;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
    font-size: 1rem;
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 8px;
}

.article-meta i {
    color: var(--secondary-color);
}

.article-stats {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.article-stats span {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.article-actions {
    position: absolute;
    top: 20px;
    left: 20px;
    display: flex;
    gap: 10px;
}

.action-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.article-image {
    height: 300px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.article-content {
    padding: 40px;
}

.article-excerpt {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.8;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
    border-right: 4px solid var(--primary-color);
}

.article-body {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-color);
    margin-bottom: 30px;
}

.article-body h2,
.article-body h3,
.article-body h4 {
    color: var(--primary-color);
    margin: 25px 0 15px 0;
}

.article-body p {
    margin-bottom: 15px;
}

.article-body ul,
.article-body ol {
    margin: 15px 0;
    padding-right: 20px;
}

.article-body li {
    margin-bottom: 8px;
}

.article-tags {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.tag {
    background: var(--primary-color);
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.tag:hover {
    background: var(--dark-color);
    color: white;
}

.article-footer {
    padding: 25px 40px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.author-details h4 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.author-details p {
    color: #666;
    font-size: 0.9rem;
}

.share-buttons {
    display: flex;
    gap: 10px;
}

.share-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 8px;
}

.share-btn.facebook {
    background: #1877f2;
    color: white;
}

.share-btn.twitter {
    background: #1da1f2;
    color: white;
}

.share-btn.whatsapp {
    background: #25d366;
    color: white;
}

.share-btn.telegram {
    background: #0088cc;
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0,0,0,0.2);
}

/* قسم التعليقات */
.comments-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.comments-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.comments-header h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.add-comment-section {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
}

.add-comment-section h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    font-size: 1rem;
    resize: vertical;
    transition: all 0.3s ease;
}

.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(29, 138, 78, 0.1);
}

.form-actions {
    display: flex;
    gap: 10px;
}

.login-message {
    text-align: center;
    padding: 20px;
    background: var(--bg-light);
    border-radius: 8px;
    margin-bottom: 20px;
}

.login-message a {
    color: var(--primary-color);
    font-weight: bold;
}

.comments-list {
    margin-top: 20px;
}

.comment-item {
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 15px;
    background: white;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-author {
    display: flex;
    align-items: center;
    gap: 10px;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.comment-info h5 {
    color: var(--primary-color);
    margin-bottom: 2px;
}

.comment-info span {
    font-size: 0.8rem;
    color: #666;
}

.comment-actions {
    display: flex;
    gap: 10px;
}

.comment-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 0.8rem;
    padding: 5px;
    transition: color 0.3s ease;
}

.comment-btn:hover {
    color: var(--primary-color);
}

.comment-text {
    line-height: 1.6;
    color: var(--text-color);
}

.no-comments {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-comments i {
    font-size: 3rem;
    color: var(--border-color);
    margin-bottom: 15px;
}

/* المقالات ذات الصلة */
.related-articles {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.related-articles h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.related-card {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.related-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
}

.related-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.related-card-content {
    padding: 15px;
}

.related-card h4 {
    color: var(--dark-color);
    margin-bottom: 8px;
    font-size: 1rem;
    line-height: 1.4;
}

.related-card h4 a {
    color: inherit;
    text-decoration: none;
}

.related-card h4 a:hover {
    color: var(--primary-color);
}

.related-card p {
    color: #666;
    font-size: 0.9rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* نافذة المشاركة */
.share-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.copy-link {
    display: flex;
    gap: 10px;
}

.copy-link input {
    flex: 1;
    padding: 10px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    font-size: 0.9rem;
}

/* التجاوب */
@media (max-width: 768px) {
    .article-header {
        padding: 25px 20px;
    }
    
    .article-title {
        font-size: 1.8rem;
    }
    
    .article-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .article-actions {
        position: static;
        margin-bottom: 20px;
    }
    
    .article-content {
        padding: 25px 20px;
    }
    
    .article-footer {
        padding: 20px;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .share-buttons {
        width: 100%;
        justify-content: center;
    }
    
    .comments-section {
        padding: 20px;
    }
    
    .related-articles {
        padding: 20px;
    }
    
    .related-grid {
        grid-template-columns: 1fr;
    }
    
    .share-options {
        grid-template-columns: 1fr;
    }
    
    .copy-link {
        flex-direction: column;
    }
}
