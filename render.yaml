services:
  - type: web
    name: tamsik-platform
    env: node
    plan: free
    buildCommand: npm install
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: DB_TYPE
        value: sqlite
      - key: JWT_SECRET
        generateValue: true
      - key: CORS_ORIGIN
        value: "*"
    healthCheckPath: /api/health
    autoDeploy: true
    branch: main 